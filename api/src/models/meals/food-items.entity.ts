import {
  Column,
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export type MacroNutrients = {
  protein: number;
  carbs: number;
  fats: number;
  calories: number;
};

export type MacroRange = {
  min: MacroNutrients;
  max: MacroNutrients;
};

@Entity('food_items')
export class FoodItemEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  category: string; // 'protein', 'carbs', etc.

  @Column('int')
  defaultServing: number;

  @Column('int', { nullable: true })
  minServing: number;

  @Column('int', { nullable: true })
  maxServing: number;

  @Column('jsonb')
  macrosPer100g: {
    protein: number;
    carbs: number;
    fats: number;
    calories: number;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
