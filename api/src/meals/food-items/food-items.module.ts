import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FoodItemEntity } from 'src/models/meals';
import { AccessTokenEntity } from 'src/models/user-entity';
import { FoodItemsController } from './food-items.controller';
import { JwtService } from '@nestjs/jwt';
import { FoodItemsService } from './food-items.service';

@Module({
  imports: [TypeOrmModule.forFeature([FoodItemEntity, AccessTokenEntity])],
  controllers: [FoodItemsController],
  providers: [FoodItemsService, JwtService],
  exports: [FoodItemsService],
})
export class FoodItemsModule {}
